<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesService;
use Illuminate\Support\Facades\Log;

/**
 * Refactored Store Distribution Strategy
 *
 * This strategy uses 90/10 split distribution for the full original quantity
 * when sales exceed the distribution limit. It skips the limited sale creation
 * and distributes the entire quantity using the split algorithm.
 * It follows SOLID principles and uses dependency injection.
 */
class StoreStrategy extends AbstractDistributionStrategy
{
    private ExcessDistributorInterface $excessDistributor;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SettingsProviderInterface $settingsProvider,
        LimitCalculatorInterface $limitCalculator,
        SaleDetailFactory $saleDetailFactory,
        SaleCreator $saleCreator,
        SalesService $salesService,
        SplitDistributionAlgorithm $excessDistributor
    ) {
        parent::__construct(
            $transactionManager,
            $settingsProvider,
            $limitCalculator,
            $saleDetailFactory,
            $saleCreator,
            $salesService
        );

        $this->excessDistributor = $excessDistributor;
    }

    /**
     * Override the template method to skip limited sale creation for Store strategy
     *
     * This method processes ceiling sales by creating only one excess sale with the full
     * original quantity and distributing it using the 90/10 split algorithm, skipping
     * the intermediate limited sale creation step.
     *
     * All operations are wrapped in a transaction to ensure data integrity. If any step
     * fails (validation, sale creation, or distribution), all changes are rolled back
     * to prevent orphaned records and maintain consistency.
     *
     * @param mixed $ceilingSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    public function processCeilingSale($ceilingSale, array $salesContributionBaseOn): bool
    {
        Log::info('StoreStrategy: Processing ceiling sale with transaction protection', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'distributor_id' => $ceilingSale->distributor_id ?? 'unknown',
            'product_id' => $ceilingSale->id ?? 'unknown',
            'quantity' => $ceilingSale->number_of_units ?? 'unknown',
            'date' => $ceilingSale->date ?? 'unknown',
            'sales_contribution_count' => count($salesContributionBaseOn)
        ]);

        try {
            return $this->transactionManager->executeInTransaction(function () use ($ceilingSale, $salesContributionBaseOn) {
                return $this->processCeilingSaleInternal($ceilingSale, $salesContributionBaseOn);
            });
        } catch (\Throwable $e) {
            Log::error('StoreStrategy: Transaction failed during ceiling sale processing', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Internal method that performs the actual ceiling sale processing within a transaction
     *
     * @param mixed $ceilingSale
     * @param array $salesContributionBaseOn
     * @return bool
     * @throws \Exception When any step fails to trigger transaction rollback
     */
    private function processCeilingSaleInternal($ceilingSale, array $salesContributionBaseOn): bool
    {
        // Step 1: Validate ceiling sale
        if (!$this->validateCeilingSale($ceilingSale)) {
            Log::warning('StoreStrategy: Ceiling sale validation failed', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
            ]);
            throw new \Exception('Ceiling sale validation failed');
        }

        Log::debug('StoreStrategy: Ceiling sale validation passed', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
        ]);

        // Step 2: Get original sale
        $originalSale = $this->getOriginalSale($ceilingSale);
        if (!$originalSale) {
            Log::error('StoreStrategy: Failed to retrieve original sale', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_ids' => $ceilingSale->sale_ids ?? 'unknown'
            ]);
            throw new \Exception('Failed to retrieve original sale');
        }

        Log::debug('StoreStrategy: Original sale retrieved successfully', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'original_sale_id' => $originalSale->id,
            'original_sale_quantity' => $originalSale->quantity
        ]);

        // Step 3: Skip limited sale creation - go directly to full quantity distribution
        Log::info('StoreStrategy: Skipping limited sale creation, proceeding with full quantity distribution', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'strategy_type' => 'STORES'
        ]);

        // Step 4: Update original sales ceiling status
        if (!$this->updateOriginalSalesCeiling($ceilingSale)) {
            Log::error('StoreStrategy: Failed to update original sales ceiling status', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_ids' => $ceilingSale->sale_ids ?? 'unknown'
            ]);
            throw new \Exception('Failed to update original sales ceiling status');
        }

        Log::debug('StoreStrategy: Original sales ceiling status updated to ABOVE', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
        ]);

        // Step 5: Create and distribute full quantity using 90/10 split
        $result = $this->createAndDistributeFullQuantitySale($ceilingSale, $originalSale, $salesContributionBaseOn);

        if (!$result) {
            Log::error('StoreStrategy: Distribution failed, triggering transaction rollback', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
            ]);
            throw new \Exception('Distribution failed - no sale details created or validation failed');
        }

        Log::info('StoreStrategy: Processing completed successfully', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'success' => $result
        ]);

        return $result;
    }

    /**
     * Create and distribute sale with full original quantity using 90/10 split distribution
     *
     * This method creates a new sale with the full quantity and attempts to distribute it.
     * Enhanced with comprehensive validation to ensure data integrity. If distribution
     * fails or validation doesn't pass, the method returns false to trigger transaction rollback.
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     * @throws \Exception When sale creation fails to trigger transaction rollback
     */
    protected function createAndDistributeFullQuantitySale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        // Use the full original quantity instead of just the excess
        $fullQuantity = $ceilingSale->number_of_units;

        Log::info('StoreStrategy: Creating full quantity sale for distribution', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'full_quantity' => $fullQuantity,
            'original_sale_id' => $originalSale->id,
            'distributor_id' => $ceilingSale->distributor_id ?? 'unknown',
            'product_id' => $ceilingSale->id ?? 'unknown'
        ]);

        try {
            $fullQuantitySale = $this->saleCreator->createExcessSale($ceilingSale, $fullQuantity);
            $fullQuantitySale = $this->saleCreator->loadRelationships($fullQuantitySale);
        } catch (\Throwable $e) {
            Log::error('StoreStrategy: Failed to create excess sale', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'error_message' => $e->getMessage()
            ]);
            throw new \Exception('Failed to create excess sale: ' . $e->getMessage());
        }

        Log::debug('StoreStrategy: Full quantity sale created', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'new_sale_id' => $fullQuantitySale->id,
            'new_sale_quantity' => $fullQuantitySale->quantity,
            'new_sale_value' => $fullQuantitySale->value,
            'new_sale_bonus' => $fullQuantitySale->bonus
        ]);

        Log::info('StoreStrategy: Starting 90/10 split distribution', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'sale_to_distribute_id' => $fullQuantitySale->id,
            'distribution_quantity' => $fullQuantitySale->quantity,
            'sales_contribution_count' => count($salesContributionBaseOn)
        ]);

        $distributionSuccess = $this->excessDistributor->distributeExcessSale(
            $fullQuantitySale,
            $salesContributionBaseOn,
            $originalSale
        );

        if ($distributionSuccess) {
            // Enhanced validation: Verify sale details were actually created and are valid
            $validationResult = $this->validateDistributionIntegrity($fullQuantitySale, $ceilingSale);

            if ($validationResult['valid']) {
                Log::debug('StoreStrategy: Distribution successful and validated, attaching mapping', [
                    'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                    'sale_id' => $fullQuantitySale->id,
                    'mapping_id' => $ceilingSale->mapping_id ?? 'unknown',
                    'details_count' => $validationResult['details_count'],
                    'quantity_match' => $validationResult['quantity_match']
                ]);

                try {
                    $this->saleCreator->attachMapping($fullQuantitySale, $ceilingSale->mapping_id);
                } catch (\Throwable $e) {
                    Log::error('StoreStrategy: Failed to attach mapping', [
                        'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                        'sale_id' => $fullQuantitySale->id,
                        'error_message' => $e->getMessage()
                    ]);
                    // Don't fail the entire process for mapping attachment failure
                }

                return true;
            } else {
                Log::error('StoreStrategy: Distribution validation failed', [
                    'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                    'sale_id' => $fullQuantitySale->id,
                    'validation_details' => $validationResult
                ]);
                return false;
            }
        } else {
            Log::error('StoreStrategy: Distribution algorithm failed', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_id' => $fullQuantitySale->id,
                'quantity' => $fullQuantitySale->quantity,
                'reason' => 'SplitDistributionAlgorithm returned false'
            ]);
            return false;
        }
    }

    /**
     * Validate distribution integrity after sale details creation
     *
     * @param Sale $sale
     * @param mixed $ceilingSale
     * @return array
     */
    private function validateDistributionIntegrity(Sale $sale, $ceilingSale): array
    {
        $sale->refresh(); // Reload to get latest data
        $detailsCount = $sale->details()->count();
        $totalDetailQuantity = $sale->details()->sum('quantity');
        $quantityMatch = abs($totalDetailQuantity - $sale->quantity) < 0.01;
        $hasDetails = $detailsCount > 0;
        $valid = $hasDetails && $quantityMatch;

        Log::debug('StoreStrategy: Distribution integrity validation', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'sale_id' => $sale->id,
            'details_count' => $detailsCount,
            'expected_quantity' => $sale->quantity,
            'actual_detail_quantity' => $totalDetailQuantity,
            'quantity_difference' => abs($totalDetailQuantity - $sale->quantity),
            'quantity_match' => $quantityMatch,
            'has_details' => $hasDetails,
            'validation_passed' => $valid
        ]);

        return [
            'valid' => $valid,
            'details_count' => $detailsCount,
            'expected_quantity' => $sale->quantity,
            'actual_detail_quantity' => $totalDetailQuantity,
            'quantity_match' => $quantityMatch,
            'has_details' => $hasDetails,
            'quantity_difference' => abs($totalDetailQuantity - $sale->quantity)
        ];
    }

    /**
     * Create and distribute excess sale using 90/10 split distribution
     *
     * @deprecated This method is kept for backward compatibility but is no longer used
     * in the Store strategy. Use createAndDistributeFullQuantitySale instead.
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    protected function createAndDistributeExcessSale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        Log::warning('StoreStrategy: Using deprecated createAndDistributeExcessSale method', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'method' => 'createAndDistributeExcessSale',
            'recommended_method' => 'createAndDistributeFullQuantitySale'
        ]);

        $excessQuantity = $this->excessDistributor->calculateExcessQuantity($ceilingSale);

        Log::debug('StoreStrategy: Calculated excess quantity (deprecated method)', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'excess_quantity' => $excessQuantity,
            'total_quantity' => $ceilingSale->number_of_units ?? 'unknown'
        ]);

        $excessSale = $this->saleCreator->createExcessSale($ceilingSale, $excessQuantity);
        $excessSale = $this->saleCreator->loadRelationships($excessSale);

        $distributionSuccess = $this->excessDistributor->distributeExcessSale(
            $excessSale,
            $salesContributionBaseOn,
            $originalSale,
            $this->getDistributionType()
        );

        if ($distributionSuccess) {
            $this->saleCreator->attachMapping($excessSale, $ceilingSale->mapping_id);
        }

        Log::info('StoreStrategy: Deprecated excess distribution completed', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'success' => $distributionSuccess,
            'excess_quantity' => $excessQuantity
        ]);

        return $distributionSuccess;
    }

    /**
     * Get the DistributionType for this strategy
     *
     * @return DistributionType
     */
    protected function getDistributionType(): DistributionType
    {
        return DistributionType::STORES;
    }
}
